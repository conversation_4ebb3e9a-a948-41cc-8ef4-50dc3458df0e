package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 是否内部项目Enum
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Getter
public enum InternalProjectEnum implements ValueEnum<Integer> {

    /**
     * 是内部项目
     */
    YES(1, "是"),

    /**
     * 不是内部项目
     */
    NO(2, "否");

    private final Integer value;

    private final String name;

    InternalProjectEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
