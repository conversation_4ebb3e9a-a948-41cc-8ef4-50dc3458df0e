package com.gok.pboot.pms.service;

import com.gok.pboot.service.commons.base.ApiResult;
import com.gok.pboot.service.entity.position.vo.GradeNodeVo;
import com.gok.pboot.service.entity.position.vo.JobTreeNodeVo;

import java.util.List;
import java.util.Map;

/**
 * Ehr服务
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
public interface EhrService {

    /**
     * 职级树
     *
     * @return {@link ApiResult }<{@link List }<{@link GradeNodeVo }>>
     */
    ApiResult<List<GradeNodeVo>> positionGradeTree();

    /**
     * 岗位树
     *
     * @return {@link ApiResult }<{@link List }<{@link JobTreeNodeVo }>>
     */
    ApiResult<List<JobTreeNodeVo>> jobTitlesTree();

    /**
     * 获取职务名称
     *
     * @return {@link Map }<{@link Long },{@link String }>
     */
    Map<Long,String> getJobActivityNameMap();

    /**
     * 获取职级名称
     *
     * @return {@link Map }<{@link Long },{@link String }>
     */
    Map<Long,String> getPositionGradeNameMap();
}
