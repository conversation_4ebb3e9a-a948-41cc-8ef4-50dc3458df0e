package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OA项目状态枚举
 *
 * <AUTHOR>
 * @date 2025/06/30
 */
@AllArgsConstructor
@Getter
public enum ProjectStatusEnum implements ValueEnum<Integer> {

    /**
     * 商机
     */
    SJ(0, "商机"),

    /**
     * 商机终止
     */
    SJZZ(1, "商机终止"),

    /**
     * 在建
     */
    ZJ(2, "在建"),

    /**
     * 挂起
     */
    GQ(3, "挂起"),


//    CY(4, "初验"),


//    ZY(5, "终验"),

    /**
     * 关闭
     */
    JX(6, "关闭"),

    /**
     * 异常终止
     */
    YCZZ(7, "异常终止"),


//    @Deprecated
//    LXSPZ(8, "立项审批中"),

    /**
     * 质保
     */
    ZB(10, "质保"),

    ;

    private final Integer value;
    private final String name;

    public String getStrValue() {
        return value.toString();
    }

    public static ProjectStatusEnum getEnumByStrVal(String val) {
        for (ProjectStatusEnum statusEnum : ProjectStatusEnum.values()) {
            if (statusEnum.value.toString().equals(val)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static String getNameByStrVal(String val) {
        for (ProjectStatusEnum statusEnum : ProjectStatusEnum.values()) {
            if (statusEnum.value.toString().equals(val)) {
                return statusEnum.name;
            }
        }
        return "";
    }
}
