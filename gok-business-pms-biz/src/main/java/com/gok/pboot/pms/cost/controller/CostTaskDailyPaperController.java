package com.gok.pboot.pms.cost.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import com.gok.pboot.pms.cost.service.ICostTaskDailyPaperService;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;
import com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 工单工时填报控制器
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@RestController
@RequestMapping("/costTaskDailyPaper")
@RequiredArgsConstructor
public class CostTaskDailyPaperController extends BaseController {

    private final ICostTaskDailyPaperService service;

    private final ICompensatoryLeaveDataService overtimeLeaveDataService;

    /**
     * 添加/编辑 日报
     *
     * @param request 请求实体
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/8/23 16:37
     */
    @PostMapping("/addOrUpdate")
    public ApiResult<Void> addOrUpdate(@Valid @RequestBody CostTaskDailyPaperAddOrUpdateDTO request) {
        service.newAddOrUpdate(request);
        request.setUserId(SecurityUtils.getUser().getId());
        return ApiResult.success(null);
    }

    /**
     * 根据ID获取报表详情
     *
     * @param id 报表ID
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/8/25 9:45
     */
    @GetMapping("/details/{id}")
    public ApiResult<CostTaskDailyPaperDetailsVO> details(@PathVariable("id") Long id) {
        return ApiResult.success(service.getDetailsById(id));
    }

    @GetMapping("/statisticMonthly")
    public ApiResult<CostTaskDailyPapersStatisticMonthlyVO> statisticMonthly(CostTaskDailyPaperMonthlyDTO request) {

        return ApiResult.success(service.getStatisticMonthly(request));
    }

    /**
     * 查询日报
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:30
     */
    @PostMapping("/listMonthly")
    public ApiResult<List<CostTaskDailyPaperVO>> listMonthly(@Valid @RequestBody CostTaskDailyPaperMonthlyDTO request) {
        return ApiResult.success(service.listDailyPaperMonthly(request));
    }

    /**
     * ~ 根据日期查找离其最近的日报详情 ~
     *
     * @param date date
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/9/5 11:20
     */
    @GetMapping("/detailsForBackFill")
    public ApiResult<CostTaskDailyPaperDetailsVO> detailsForBackFill(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUser(date));
    }

    /**
     * 根据日期查询日报信息
     *
     * @param date 日期
     * @return {@link ApiResult<DailyPaperDetailsVO>}
     */
    @GetMapping("/detailsByDate")
    public ApiResult<CostTaskDailyPaperDetailsVO> detailsBySubmissionDate(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUserAccurate(date));
    }


    /**
     * 定时器
     * ~ 自动提交前一日日报定时任务调用方法 ~    0 0 14 * * ? *
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/16 14:38
     */
    @Inner(value = false)
    @GetMapping("/autoSubmitJob")
    public ApiResult<Void> autoSubmitJob(@RequestParam("para") String para) {
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行开始", para);
        service.autoSubmitJob(para);
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行结束", para);
        return ApiResult.success(null);
    }

    /**
     * 查询日报
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:30
     */
    public ApiResult<List<CompensatoryLeaveDataVO>> test() {

        List<CompensatoryLeaveDataVO> byDateTimeRangeAndUserId =
                overtimeLeaveDataService.findByDateTimeRangeAndUserId(LocalDate.of(2024, 4, 16),
                        null, 1219L);
        return ApiResult.success(byDateTimeRangeAndUserId);
    }

    /**
     * 查询异常工时列表
     *
     * @param pageRequest 分页参数
     * @param dto         查询条件
     * @return {@link Page}<{@link com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperAbnormalVO}> 异常工时列表
     */
    @GetMapping("/findPage/abnormal")
    public ApiResult<Page<CostTaskDailyPaperAbnormalVO>> abnormalPage(PageRequest pageRequest, CostTaskDailyPaperAbnormalDTO dto) {
        return ApiResult.success(service.findAbnormalPage(pageRequest, dto));
    }

    /**
     * 获取异常工时计数
     *
     * @param dto DTO
     * @return {@link ApiResult }<{@link Map }<{@link Integer }, {@link Integer }>>
     */
    @GetMapping("/getAbnormalCount")
    public ApiResult<Map<Integer, Integer>> getAbnormalCount(CostTaskDailyPaperAbnormalDTO dto) {
        return ApiResult.success(service.getAbnormalCount(dto));
    }

    /**
     * 导出异常工时
     *
     * @param dto 查询条件
     * @return {@link List}<{@link com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperAbnormalVO}>
     */
    @ResponseExcel(name = "异常工时")
    @GetMapping("/export/abnormalPaper")
    public List<CostTaskDailyPaperAbnormalVO> exportAbnormalPaper(CostTaskDailyPaperAbnormalDTO dto) {
        return service.exportAbnormalPaper(dto);
    }

    /**
     * 发送异常工时消息推送
     *
     * @param dto 查询条件
     * @return 操作结果
     */
    @PostMapping("/sendAbnormalMsg")
    public ApiResult<String> sendAbnormalMsg(@RequestBody CostTaskDailyPaperAbnormalDTO dto) {
        service.sendAbnormalMsg(dto);
        return ApiResult.success("推送成功");
    }

    /**
     * 发送异常工时消息推送
     ** @return 操作结果
     */
    @GetMapping("/task/sendAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> taskSendAbnormalMsg() {
        service.taskSendAbnormalMsg(TaskAbnormalTypeEnum.UN_REVIEWED);
        return ApiResult.success("推送成功");
    }
    /**
     * 发送未评价异常工单消息推送
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/task/sendUnEvaluatedAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> sendUnEvaluatedAbnormalMsg() {
        service.taskSendAbnormalMsg(TaskAbnormalTypeEnum.UN_EVALUATED);
        return ApiResult.successMsg("推送成功");
    }

    /**
     * 根据工单ID获取日报详情
     *
     * @param pageRequest 分页参数
     * @param taskId      工单ID
     * @return 日报详情分页数据
     */
    @GetMapping("/daily-details/{taskId}")
    public ApiResult<Page<TaskDailyPaperDetailVO>> getDailyDetailsByTaskId(
            PageRequest pageRequest,
            @PathVariable Long taskId,
            @RequestParam(required = false) Integer approvalStatus) {
        return ApiResult.success(service.getDailyDetailsByTaskId(pageRequest, taskId,approvalStatus));
    }


    /**
     * 工单审核人信息和相关日报分页查询
     *
     * @return {@link ApiResult }<{@link Page }<{@link TaskDailyPaperDetailVO }>>
     */
    @GetMapping("/taskDailyPaperPage/{taskId}")
    public ApiResult<CostTaskDailyDetailVO> taskDailyPaperPage(
            PageRequest pageRequest,
            @PathVariable Long taskId,
            @RequestParam(required = false,defaultValue = "2") Integer approvalStatus) {

        return ApiResult.success(service.taskDailyPaperPage(pageRequest, taskId, approvalStatus));
    }


    /**
     * 售前工单工时审核分页列表
     *
     * @param pageRequest 分页参数
     * @param dto         查询条件
     * @return 分页结果
     */
    @GetMapping("/findSupportTaskApprovalPage")
    public ApiResult<Page<CostSupportTaskApprovalVO>> findSupportTaskApprovalPage(PageRequest pageRequest, CostSupportTaskApprovalDTO dto) {
        return ApiResult.success(service.findSupportTaskApprovalPage(pageRequest, dto));
    }

    /**
     * 查询项目工单工时审核列表
     *
     * @param dto 查询条件
     * @return 工单工时审核列表
     */
    @GetMapping("/findTaskDailyPaperApprovalList")
    public ApiResult<CostTaskDailyPaperApprovalVO> findTaskDailyPaperApprovalList(PageRequest pageRequest, CostSupportTaskApprovalDTO dto) {
        return ApiResult.success(service.findTaskDailyPaperApprovalList(pageRequest, dto));
    }

    /**
     * 改变工单工时日报的审核状态
     *
     * @param dto
     * @customParam approvalStatus 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已审核）
     * @customParam id 日报条目Id
     */
    @PostMapping("/changeApprovalStatus")
    public ApiResult<String> changeApprovalStatus(@RequestBody CostChangeApprovalStatusDTO dto) {
        return service.changeApprovalStatus(dto);
    }


    /**
     * 批量审核
     * @param dailyPaperIds 日报id列表用,分割
     * @return
     */
    @PostMapping("/batchApproval")
    public ApiResult<String> batchApproval(@RequestParam String dailyPaperIds) {
        return service.batchApproval(dailyPaperIds);
    }
} 