package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 项目核心数据Dto
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentDataDTO implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Long id;

    /**
     * 偏差说明
     */
    private String deviationExplanation;

    /**
     * 成本偏差和毛利偏差说明
     */
    private String costGrossDeviationExplanation;

    /**
     * 待回款说明
     */
    private String pendingAmountExplanation;

    /**
     * 现金流说明
     */
    private String cashExplanation;

    /**
     * 坏账说明
     */
    private String badDebtExplanation;

}
