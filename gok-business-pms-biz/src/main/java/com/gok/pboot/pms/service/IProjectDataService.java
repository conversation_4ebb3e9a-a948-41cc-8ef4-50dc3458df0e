package com.gok.pboot.pms.service;

import com.gok.pboot.pms.entity.dto.PaymentDataDTO;
import com.gok.pboot.pms.entity.vo.ProjectDataVO;

/**
 * 项目核心数据服务
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
public interface IProjectDataService {

    /**
     * 根据项目ID查询
     *
     * @param id 项目ID
     * @return data
     */
    ProjectDataVO findById(Long id);

    /**
     * 根据项目ID获取质保确认/关闭确认数据
     *
     * @param id 项目ID
     * @return
     */
    ProjectDataVO findConfirmedDataByProjectId(Long id);

    /**
     * 更新项目偏差说明
     *
     * @param request
     * @return
     */
    Long updateDeviation(PaymentDataDTO request);

}
