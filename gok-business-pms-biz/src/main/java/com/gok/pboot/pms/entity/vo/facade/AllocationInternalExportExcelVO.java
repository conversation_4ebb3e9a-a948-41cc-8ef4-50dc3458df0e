package com.gok.pboot.pms.entity.vo.facade;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.entity.vo.AllocationFindPageVO;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * - 工时分摊导出 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AllocationInternalExportExcelVO {

    /**
     * 项目编号
     */
    @ExcelProperty(index = 0, value = "项目编号")
    private String code;

    /**
     * 项目名称
     */
    @ExcelProperty(index = 1, value = "项目名称")
    private String projectName;
    /**
     * 项目状态值（0=商机，1=商机终止，2=在建，3=挂起，4=初验，5=终验，6=结项，7=异常终止）
     */
    @ExcelProperty(index = 2, value = "项目状态")
    private String projectStatusName;

    @ExcelProperty(index = 3, value = "是否内部项目")
    private String isNotInternalProjectStr;
    /**
     * 项目类型
     */
    @ExcelProperty(index = 4, value = "项目类型")
    private String projectTypeName;

    /**
     * 收入归属部门
     */
    @ExcelProperty(index = 5, value = "收入归属部门")
    private String projectDeptName;
    /**
     * 姓名
     */
    @ExcelProperty(index = 6, value = "姓名")
    private String name;

    /**
     * 员工工号
     */
    @ExcelProperty(index = 7, value = "员工工号")
    private String workCode;

    /**
     * 身份证号
     */
    @ExcelIgnore
    private String idCardNo;

    /**
     * 实际出勤天数
     */
    @ExcelProperty(index = 8, value = "出勤天数")
    private String cwActualAttendance;

    @ExcelProperty(index = 9, value = "项目正常工时(小时)")
    private String projectNormalHours;


    @ExcelProperty(index = 10, value = "总加班工时(小时)")
    private String projectAddedHours;

    @ExcelProperty(index = 11, value = "工作日加班工时(小时)")
    private String workOvertimeHours;

    @ExcelProperty(index = 12, value = "休息日加班工时(小时)")
    private String restOvertimeHours;

    @ExcelProperty(index = 13, value = "节假日加班工时(小时)")
    private String holidayOvertimeHours;

    /**
     * 调休工时
     */
    @ExcelProperty(index = 14, value = "调休工时(小时)")
    private String leaveHours;

    /**
     * 项目耗用工时
     */
    @ExcelProperty(index = 15, value = "项目分摊工时(小时)")
    private String projectHours;

    /**
     * 售前工时
     */
    @ExcelProperty(index = 16, value = "售前工时(小时)")
    private String preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty(index = 17, value = "售后工时(小时)")
    private String afterSaleHours;



    /**
     * 人员状态名称
     */
    @ExcelProperty(index = 18, value = "状态")
    private String personnelStatusName;

    /**
     * 人员归属部门全称
     */
    @ExcelProperty(index = 19, value = "人员归属部门")
    private String personnelDeptName;

    /**
     * 工时审核员
     */
    @ExcelIgnore
    private String privilegeUserName;

    public static AllocationInternalExportExcelVO fromHour(AllocationFindPageVO findPageVO){
        AllocationInternalExportExcelVO vo = new AllocationInternalExportExcelVO();
        String zero = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();

        vo.setCode(findPageVO.getCode());
        vo.setName(findPageVO.getName());
        vo.setIdCardNo(Strings.nullToEmpty(findPageVO.getIdCardNo()));
        vo.setProjectName(findPageVO.getProjectName());
        vo.setProjectStatusName(findPageVO.getProjectStatusName());
        vo.setIsNotInternalProjectStr(findPageVO.getIsNotInternalProjectStr());
        vo.setProjectTypeName(findPageVO.getProjectTypeName());
        vo.setProjectDeptName(findPageVO.getProjectDeptName());
        vo.setCwActualAttendance(findPageVO.getCwActualAttendance() == null ? zero :findPageVO.getCwActualAttendance().toPlainString());
        vo.setProjectNormalHours(findPageVO.getProjectNormalHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getProjectNormalHours()).toPlainString());
        vo.setProjectAddedHours(findPageVO.getProjectAddedHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getProjectAddedHours()).toPlainString());
        vo.setWorkOvertimeHours(findPageVO.getWorkOvertimeHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getWorkOvertimeHours()).toPlainString());
        vo.setRestOvertimeHours(findPageVO.getRestOvertimeHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getRestOvertimeHours()).toPlainString());
        vo.setHolidayOvertimeHours(findPageVO.getHolidayOvertimeHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getHolidayOvertimeHours()).toPlainString());
        vo.setLeaveHours(findPageVO.getLeaveHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getLeaveHours()).toPlainString());
        vo.setProjectHours(findPageVO.getProjectShareHours() == null ? zero : CommonUtils.roundOnePoint(findPageVO.getProjectShareHours()).toPlainString());
        vo.setPreSaleHours(findPageVO.getPreSaleHours() == null ? zero : CommonUtils.roundOnePoint(new BigDecimal(findPageVO.getPreSaleHours())).toPlainString());
        vo.setAfterSaleHours(findPageVO.getAfterSaleHours() == null ? zero : CommonUtils.roundOnePoint(new BigDecimal(findPageVO.getAfterSaleHours())).toPlainString());
        vo.setPersonnelStatusName(findPageVO.getPersonnelStatusName());
        vo.setPersonnelDeptName(findPageVO.getPersonnelDeptName());
        vo.setPrivilegeUserName(findPageVO.getPrivilegeUserName());
        vo.setWorkCode(findPageVO.getWorkCode());
        return vo;
    }


}
