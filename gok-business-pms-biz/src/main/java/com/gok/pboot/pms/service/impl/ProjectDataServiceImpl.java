package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectData;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.PaymentDataDTO;
import com.gok.pboot.pms.entity.vo.FinancialDataVo;
import com.gok.pboot.pms.entity.vo.ProjectContractVo;
import com.gok.pboot.pms.entity.vo.ProjectDataVO;
import com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo;
import com.gok.pboot.pms.mapper.ProjectDataMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.IProjectDataService;
import com.gok.pboot.pms.service.IProjectWorkHoursService;
import com.gok.pboot.pms.service.ProjectDetailsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 项目核心数据服务
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Service
@RequiredArgsConstructor
public class ProjectDataServiceImpl extends ServiceImpl<ProjectDataMapper, ProjectData> implements IProjectDataService {

    private final ProjectDataMapper mapper;

    private final DbApiUtil dbApiUtil;

    private final ProjectInfoMapper projectInfoMapper;
    private final IProjectWorkHoursService projectWorkHoursService;
    private final ProjectDetailsService projectDetailsService;

    @Override
    public ProjectDataVO findById(Long id) {
        // 获取项目核心数据
        ProjectData po = mapper.selectById(id);
        // 获取项目信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(id);
        // 判断是否为内部项目
        boolean internalFlag = false;
        if (Optional.ofNullable(projectInfo).isPresent()) {
            internalFlag = projectInfo.getIsNotInternalProject() == 1 ? true : false;
        }
        ProjectDataVO result;
        // 项目核心数据为空返回
        if (po == null) {
            return new ProjectDataVO();
        }
        // 实体数据转换
        result = ProjectDataVO.of(po, internalFlag);
        if (internalFlag) {
            //预算成本
            result.setTotalBudgetCostIncludeTax(DecimalFormatUtil.setThousandthAndTwoDecimal(projectInfo.getEstimatedCost(), DecimalFormatUtil.ZERO));
            //实际总成本
            result.setActualTotalBudgetCost(DecimalFormatUtil.setThousandthAndTwoDecimal(BigDecimal.ZERO, DecimalFormatUtil.ZERO));
            //预算成本
            BigDecimal totalBudgetCostIncludeTax = projectInfo.getEstimatedCost() == null ? BigDecimal.ZERO : projectInfo.getEstimatedCost();
            //实际总成本
            BigDecimal actualTotalBudgetCost = new BigDecimal(StrUtil.isNotBlank(result.getActualTotalBudgetCost()) ? result.getActualTotalBudgetCost() : "0.00");
            //剩余可用预算
            result.setRemainingAvailableBudget(DecimalFormatUtil.setThousandthAndTwoDecimal(totalBudgetCostIncludeTax.subtract(actualTotalBudgetCost), DecimalFormatUtil.ZERO));
            //成本进度
            result.setCostProgress("0%");
            if (!totalBudgetCostIncludeTax.equals(BigDecimal.ZERO)
                    && !totalBudgetCostIncludeTax.equals(new BigDecimal("0.00"))
                    && !totalBudgetCostIncludeTax.equals(new BigDecimal("0.0"))) {
                result.setCostProgress(actualTotalBudgetCost
                        .divide(totalBudgetCostIncludeTax, 0, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal(100)) + "%");
            }


            // 内部项目预估总人天
            BigDecimal estimatedTotalManDays = new BigDecimal(result.getInternalEstimatedTotalManDays());
            result.setEstimatedTotalManDays(estimatedTotalManDays.setScale(2, RoundingMode.HALF_UP).toString());

            //已发生人天(取自本系统本项目审核通过的工时合计)
            Map<String, Object> filter = new HashMap<>();
            filter.put("projectId", id);
            ProjectWorkHoursVo avgWorkHoursVo = projectWorkHoursService.calculateAvgWorkHours(filter);
            BigDecimal manDays = BigDecimal.ZERO;
            if (Optional.ofNullable(avgWorkHoursVo).isPresent() && Optional.ofNullable(avgWorkHoursVo.getAvgSumHours()).isPresent()) {
                manDays = avgWorkHoursVo.getAvgSumHours();
            }
            result.setManDays(manDays.setScale(2, RoundingMode.HALF_UP).toString());

            //剩余可用人天
            result.setRemainingAvailableDays(estimatedTotalManDays.subtract(manDays).setScale(2, RoundingMode.HALF_UP).toString());

        } else {
            // 财务数据处理
            String projectIdStr = String.valueOf(id);
            FinancialDataVo financialData = dbApiUtil.financialData(projectIdStr, StrUtil.EMPTY, StrUtil.EMPTY);
            ProjectDataVO.update(financialData, result);

            // 外部项目预估总人天
            BigDecimal estimatedTotalManDays = new BigDecimal(result.getEstimatedTotalManDays());
            result.setEstimatedTotalManDays(estimatedTotalManDays.setScale(2, RoundingMode.HALF_UP).toString());

            // 合同数据处理
            Map<String, Object> contractFilter = new HashMap<>();
            contractFilter.put("projectId", projectIdStr);
            contractFilter.put("contractType", "1");
            ProjectContractVo projectContractVo = projectDetailsService.projectContract(new PageRequest(1, Integer.MAX_VALUE), contractFilter).getData();
            ProjectDataVO.update(projectContractVo, result);

            // 实际已发生人天
            Map<String, Object> projectWorkHoursFilter = new HashMap<>();
            projectWorkHoursFilter.put("projectId", projectIdStr);
            projectWorkHoursFilter.put("dimension", 0);
            ProjectWorkHoursVo projectWorkHoursVo = projectWorkHoursService.countWorkHours(new PageRequest(1, 1), projectWorkHoursFilter);
            result.setManDays(null != projectWorkHoursVo.getAvgSumHours() ? projectWorkHoursVo.getAvgSumHours().toString() : "0.00");
        }

        return result;
    }

    @Override
    public ProjectDataVO findConfirmedDataByProjectId(Long id) {
        ProjectData po = mapper.selectById(id);
        if (null == po) {
            return new ProjectDataVO();
        }

        ProjectDataVO result = BeanUtil.copyProperties(po, ProjectDataVO.class);
        return result;
    }

    @Override
    @Transactional
    public Long updateDeviation(PaymentDataDTO request) {
        Long projectId = request.getId();
        ProjectData po = mapper.selectById(projectId);
        if (null == po) {
            return projectId;
        }

        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
//        Long currentUserId = SecurityUtils.getUser().getId();
//        if (!currentUserId.equals(projectInfo.getManagerUserId())){
//            throw new BusinessException("当前用户无权限");
//        }

        po.setDeviationExplanation(request.getDeviationExplanation());
        po.setCostGrossDeviationExplanation(request.getCostGrossDeviationExplanation());
        po.setPendingAmountExplanation(request.getPendingAmountExplanation());
        po.setCashExplanation(request.getCashExplanation());
        po.setBadDebtExplanation(request.getBadDebtExplanation());
        baseMapper.updateById(po);

        return projectId;
    }

}
