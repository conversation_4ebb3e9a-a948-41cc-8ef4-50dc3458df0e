package com.gok.pboot.pms.oa;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.vo.OaFileVo;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.OaGetTokenDTO;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.util.*;

/**
 * OA接口接入
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Component
@Slf4j
public class OaUtil {

    /**
     * oa的url地址
     */
    @Value("${oa.url.httpUrl}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${oa.spk}")
    private String spk;

    /**
     * oa的secret
     */
    @Value("${oa.secret}")
    private String secret;

    /**
     * 返回的状态字段
     */
    public static final String CODE = "code";
    /**
     * 是否获取成功的字段
     */
    public static final String SUCCESS = "SUCCESS";
    /**
     * 获取到的字段
     */
    public static final String DATA = "data";

    @Resource
    private OaClient oaClient;

    /**
     * 获取token
     */
    public String getToken() {
        OaGetTokenDTO oaClientToken = oaClient.getToken(url, appId, secret);
        String token = oaClientToken.getToken();
        if (StringUtils.isEmpty(token)) {
            throw new ServiceException("OA调用异常，无法获取token信息");
        }
        return token;
    }

    /**
     * 获取流程相关资源
     */
    public Map getRequestResources(String requestId, String userId) {
        // 获取token
        String token = getToken();
        if (userId == null) {
            throw new ServiceException("必须传入userID");
        }
        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(userId, spk);
        Map requestResources = oaClient.getRequestResources(url, requestId, token, appId, encryptUserId);
        return requestResources;
    }

    /**
     * 获取OA返回的资源数据
     */
    public List<OaFileVo> getResourcesData(String requestId, String userId) {
        Map requestResources = this.getRequestResources(requestId, userId);

        if (SUCCESS.equals(requestResources.get(CODE))) {
            JSONArray data = (JSONArray) requestResources.get(DATA);
            return data.toJavaList(OaFileVo.class);
        }

        return ImmutableList.of();
    }


    /**
     * 修改OA项目台账
     * restful接口调用案例
     * 以getModeDataPageList为例
     */
    public void doAction(String id, String field, String value) {

        CloseableHttpResponse response;// 响应类,
        CloseableHttpClient httpClient = HttpClients.createDefault();

        //restful接口url
        HttpPost httpPost = new HttpPost(url + "/api/cube/restful/interface/saveOrUpdateModeData/updateProject");

        //当前日期
        String currentDate = getCurrentDate();
        //当前时间
        String currentTime = getCurrentTime();
        //获取时间戳
        String currentTimeTamp = getTimestamp();

        Map<String, Object> params = new HashMap<>();
        Map paramDatajson = new HashMap<>();

        //header
        Map header = new HashMap<>();
        Map data = new HashMap<>();

        //系统标识
        String systemid = "admin";
        //密码
        String d_password = "gok123456";
        //封装header里的参数
        header.put("systemid", systemid);
        header.put("currentDateTime", currentTimeTamp);
        String md5Source = systemid + d_password + currentTimeTamp;
        String md5OfStr = getMD5Str(md5Source).toLowerCase();
        //Md5是：系统标识+密码+时间戳 并且md5加密的结果
        header.put("Md5", md5OfStr);
        paramDatajson.put("header", header);

        //封装pageinfo
//        JSONObject pageInfo = new JSONObject();
//        pageInfo.put("pageNo", 1);
//        pageInfo.put("pageSize", 10);
//        data.put("pageInfo",pageInfo);

        //封装mainTable参数
        JSONObject mainTable = new JSONObject();
        mainTable.put("id", id);
        mainTable.put(field, value);
        data.put("mainTable", mainTable);

        //封装operationinfo参数
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("operator", "1");
        data.put("operationinfo", operationinfo);
        paramDatajson.put("data", Lists.newArrayList(data));

        System.out.println("===请求参数datajson===" + paramDatajson);
        params.put("datajson", paramDatajson);
        //装填参数
        List nvps = new ArrayList();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), JSONObject.toJSONString(entry.getValue())));
            }
        }
        try {
            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
            System.out.println(nvps);
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
            response = httpClient.execute(httpPost);
            if (response != null && response.getEntity() != null) {
                //返回信息
                String resulString = EntityUtils.toString(response.getEntity());

                //todo这里处理返回信息

                System.out.println("成功" + resulString);


            } else {
                System.out.println("获取数据失败，请查看日志" + currentDate + " " + currentTime);
            }
        } catch (Exception e) {
            System.out.println("请求失败" + currentDate + " " + currentTime + "====errormsg:" + e.getMessage());
        }


    }

    public static String getMD5Str(String plainText) {
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            //throw new RuntimeException("没有md5这个算法！");
            throw new RuntimeException();
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    public static String getCurrentTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        String currenttime = (timestamp.toString()).substring(11, 13) + ":" + (timestamp.toString()).substring(14, 16) + ":"
                + (timestamp.toString()).substring(17, 19);
        return currenttime;
    }

    public static String getCurrentDate() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        String currentdate = (timestamp.toString()).substring(0, 4) + "-" + (timestamp.toString()).substring(5, 7) + "-"
                + (timestamp.toString()).substring(8, 10);
        return currentdate;
    }

    /**
     * 获取当前日期时间。 YYYY-MM-DD HH:MM:SS
     *
     * @return 当前日期时间
     */
    public static String getCurDateTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        return (timestamp.toString()).substring(0, 19);
    }

    /**
     * 获取时间戳   格式如：19990101235959
     *
     * @return
     */
    public static String getTimestamp() {
        return getCurDateTime().replace("-", "").replace(":", "").replace(" ", "");
    }

}
