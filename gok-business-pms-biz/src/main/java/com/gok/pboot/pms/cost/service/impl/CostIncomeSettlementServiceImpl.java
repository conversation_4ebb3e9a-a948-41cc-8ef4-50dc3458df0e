package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.base.admin.feign.RemoteOaService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.RedisConstant;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlement;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.CostRequestStatusEnum;
import com.gok.pboot.pms.cost.enums.DataSourcesEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeSettlementDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeSettlementMapper;
import com.gok.pboot.pms.cost.service.ICostIncomeSettlementService;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.vo.OaAccountVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.OaCreateRequestDTO;
import com.gok.pboot.pms.oa.dto.OaMainParamDTO;
import com.gok.pboot.pms.service.IDictService;
import com.google.common.base.Charsets;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CostIncomeSettlementServiceImpl extends ServiceImpl<CostIncomeSettlementMapper, CostIncomeSettlement> implements ICostIncomeSettlementService {

    private final DbApiUtil dbApiUtil;
    private final IDictService idictService;
    private final StringRedisTemplate stringRedisTemplate;
    private final CostIncomeSettlementDetailMapper settlementDetailMapper;
    private final CostIncomeCalculationMapper calculationMapper;
    private final CostIncomeCalculationDetailMapper calculationDetailMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final RemoteOaService remoteOaService;
    private final RemoteRoleService remoteRoleService;
    private final RemoteUserService remoteUserService;
    private final ProjectBusinessMilestonesMapper projectBusinessMilestonesMapper;
    private final ProjectScopeHandle projectScopeHandle;
    private static final String menuCode = "DELIVERY_HUMAN_RESOURCE_SRJS";
    private final OaUtil oaUtil;
    private final OaClient oaClient;
    @Value("${oa.workflowUrl}")
    private String workflowUrl;
    @Value("${oa.url.httpUrl}")
    private String url;
    @Value("${oa.resourcesAppId}")
    private String appId;
    @Value("${oa.spk}")
    private String spk;
    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    @Value("${pushMessage.costIncomeSettlementUrlRedirect}")
    private String costIncomeSettlementUrlRedirect;

    @Override
    public List<CostIncomeSettlementVO> findList(CostIncomeSettlementListDTO dto) {
        //查询数据权限范围
        SysUserDataScopeVO dataPermission = projectScopeHandle.getDeliverManagementDataPermission(menuCode, dto.getProjectId(), null);
        if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
            dto.setPurviewNames(dataPermission.getUserNameList());
            dto.setPurview(Boolean.TRUE);
        }

        //查询收入结算明细数据
        List<CostIncomeSettlementDetailVO> detailVOList = settlementDetailMapper.selList(dto);
        Table<String, String, List<CostIncomeSettlementDetailVO>> detailTable = HashBasedTable.create();
        if (CollectionUtil.isNotEmpty(detailVOList)){
            detailVOList.stream().forEach(d ->{
                List<CostIncomeSettlementDetailVO> exists = detailTable.get(d.getSettlementNumber(),d.getTaxRate());
                if (CollectionUtil.isEmpty(exists)) {
                    detailTable.put(d.getSettlementNumber(),d.getTaxRate(), Lists.newArrayList(d));
                } else {
                    exists.add(d);
                }
            });
            List<String>  settlementNumbers = detailVOList.stream().map(CostIncomeSettlementDetailVO::getSettlementNumber).collect(Collectors.toList());
            dto.setPurviewNumbers(settlementNumbers);
        }
        List<CostIncomeSettlementVO> voList = baseMapper.selList(dto);
        if (CollectionUtil.isEmpty(voList)){
            return ListUtil.empty();
        }
        Map<String, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getValue,DictKvVo::getName));
        Table<String, String, List<CostIncomeSettlementDetailVO>> detailVOTable = HashBasedTable.create();
        detailVOList.stream().forEach(d ->{
            List<CostIncomeSettlementDetailVO> exists = detailVOTable.get(d.getSettlementNumber(),d.getTaxRate());
            if (CollectionUtil.isEmpty(exists)) {
                detailVOTable.put(d.getSettlementNumber(),d.getTaxRate(), Lists.newArrayList(d));
            } else {
                exists.add(d);
            }
        });
        List<Long> requestIds = detailVOList.stream()
                .filter(version -> version != null && version.getRequestId() != null)
                .map(CostIncomeSettlementDetailVO::getRequestId)
                .collect(Collectors.toList());
        voList.stream()
                .filter(version -> version != null && version.getRequestId() != null)
                .forEach(e -> {
                    List<String> strings = Arrays.asList(e.getRequestId().split(","));
                    strings.stream().forEach(requestId -> {
                        Long aLong = Long.valueOf(requestId);
                        if (!requestIds.contains(aLong)){
                            requestIds.add(aLong);
                        }
                    });
                });
        List<CostManageVersionVO> requestVersionList = dbApiUtil.getOaRequestStatusToObj(requestIds,CostManageVersionVO.class);
        Map<Long, CostManageVersionVO> requestMap = requestVersionList.stream().collect(Collectors.toMap(CostManageVersionVO::getRequestId, a -> a, (a, b) -> a));
        voList.forEach(e ->{
            e.setTaxRateTxt(Optional.ofNullable(e.getTaxRate()).isPresent() ? taxRateMap.get(e.getTaxRate()) : null);
            e.setDataSourcesTxt(EnumUtils.getNameByValue(DataSourcesEnum.class,e.getDataSources()));
            e.setApprovalStatus(CostRequestStatusEnum.UNCOMMIT.getValue());
            e.setApprovalStatusTxt(CostRequestStatusEnum.UNCOMMIT.getName());
            //根据结算编号和税率获取明细数据
            List<CostIncomeSettlementDetailVO> detailList = detailVOTable.get(e.getSettlementNumber(), e.getTaxRate());
            String requestId = e.getRequestId();
            if (Optional.ofNullable(requestId).isPresent()) {
                List<String> requestIdList = Arrays.asList(requestId.split(","));
                Integer requestIdNum = NumberUtils.INTEGER_ZERO;
                Set<Long> detailRequestIdList = new HashSet<>();
                if (CollectionUtil.isNotEmpty(detailList)) {
                    List<Long> haveRequestIdList = detailList.stream()
                            .filter(version -> version != null && version.getRequestId() != null)
                            .map(CostIncomeSettlementDetailVO::getRequestId).collect(Collectors.toList());
                    detailRequestIdList.addAll(haveRequestIdList);
                    List<CostIncomeSettlementDetailVO> notRequestIdList = detailList.stream()
                            .filter(version -> version != null && version.getRequestId() == null).collect(Collectors.toList());
                    requestIdNum = detailRequestIdList.size() + notRequestIdList.size();
                }
                List<Integer> requestStatusList = new ArrayList<>();
                List<String> filingTimeList = new ArrayList<>();

                StringBuilder requestNumber = new StringBuilder();
                String filingTime = StrUtil.EMPTY;
                List<RequestVO> requestList = new ArrayList<>();
                requestIdList.stream().forEach(d -> {
                    CostManageVersionVO reqVersion = requestMap.getOrDefault(Long.valueOf(d.trim()), null);
                    if (Optional.ofNullable(reqVersion).isPresent()) {
                        requestStatusList.add(reqVersion.getRequestStatus());
                        if (StrUtil.isNotBlank(reqVersion.getRequestNumber())) {
                            requestNumber.append(reqVersion.getRequestNumber() + "<br/>");
                        }
                        filingTimeList.add(reqVersion.getFilingTime());
                        RequestVO requestVO = RequestVO.builder().requestId(d).requestNumber(reqVersion.getRequestNumber()).build();
                        requestList.add(requestVO);
                    }
                });

                if (CollectionUtil.isNotEmpty(requestStatusList)) {
                    if (requestStatusList.stream().allMatch(r -> CostRequestStatusEnum.UNCOMMIT.getCurrentNodeType().equals(r))) {
                        e.setApprovalStatus(CostRequestStatusEnum.UNCOMMIT.getValue());
                        e.setApprovalStatusTxt(CostRequestStatusEnum.UNCOMMIT.getName());
                    } else if (requestIdList.size() < requestIdNum || requestStatusList.contains(CostRequestStatusEnum.UNCOMMIT.getCurrentNodeType())) {
                        e.setApprovalStatus(CostRequestStatusEnum.PARTIALLY_COMMIT.getValue());
                        e.setApprovalStatusTxt(CostRequestStatusEnum.PARTIALLY_COMMIT.getName());
                    } else if (requestStatusList.contains(CostRequestStatusEnum.INAPPROVAL.getCurrentNodeType())) {
                        e.setApprovalStatus(CostRequestStatusEnum.INAPPROVAL.getValue());
                        e.setApprovalStatusTxt(CostRequestStatusEnum.INAPPROVAL.getName());
                    } else if (requestStatusList.stream().allMatch(r -> CostRequestStatusEnum.FINISH.getCurrentNodeType().equals(r))) {
                        e.setApprovalStatus(CostRequestStatusEnum.FINISH.getValue());
                        e.setApprovalStatusTxt(CostRequestStatusEnum.FINISH.getName());
                        if (CollectionUtil.isNotEmpty(filingTimeList)) {
                            List<LocalDate> startDateList = filingTimeList.stream()
                                    .map(dateString -> LocalDate.parse(dateString))
                                    .distinct().collect(Collectors.toList());
                            filingTime = startDateList.stream().max(LocalDate::compareTo).get().toString();
                        }
                    }
                    e.setRequestNumber(Optional.ofNullable(requestNumber).isPresent() ? requestNumber.toString() : StrUtil.EMPTY);
                    e.setFilingTime(filingTime);
                    e.setRequestList(requestList);
                }
            }
            e.setIsEdit(Boolean.FALSE);
            if (CollectionUtil.isEmpty(detailTable.get(e.getSettlementNumber(), e.getTaxRate()))) {
                e.setIsEdit(Boolean.TRUE);
            }
        });
        return voList;
    }

    @Override
    public List<CostIncomeSettlement> addOrUpdate(List<CostIncomeSettlementEditDTO> dtoList) {
        List<CostIncomeSettlement> costIncomeSettlementList = new ArrayList<>();
        if (CollectionUtil.isEmpty(dtoList)){
            return ListUtil.empty();
        }
        // 获取税率字典
        Map<String,String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getValue,DictKvVo::getName));
        dtoList.stream().forEach(d ->{
            //税率
            BigDecimal taxRate = Optional.ofNullable(d.getTaxRate()).isPresent()?
                    new BigDecimal(taxRateMap.getOrDefault(d.getTaxRate(), null).replaceAll("%", "")).divide(new BigDecimal(100)):BigDecimal.ZERO;
            //结算不含税金额
            BigDecimal budgetAmountExcludingTax = d.getBudgetAmountIncludedTax().divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP);
            d.setBudgetAmountExcludingTax(budgetAmountExcludingTax);
        });
        List<CostIncomeSettlementEditDTO> updateList = dtoList.stream().filter(dto -> Optional.ofNullable(dto.getId()).isPresent()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)){
            List<Long> detailsIds = updateList.stream().map(CostIncomeSettlementEditDTO::getId).collect(Collectors.toList());
            Map<Long, CostIncomeSettlement> detailMapById = baseMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery()
                    .in(CostIncomeSettlement::getId, detailsIds))
                    .stream().collect(Collectors.toMap(CostIncomeSettlement::getId, a -> a));
            List<CostIncomeSettlement> updateSettlementList = updateList.stream().map(u -> {
                CostIncomeSettlement detail = detailMapById.get(u.getId());
                BeanUtil.copyProperties(u, detail);
                BaseBuildEntityUtil.buildUpdate(detail);
                return detail;
            }).collect(Collectors.toList());
            costIncomeSettlementList.addAll(updateSettlementList);
        }
        List<CostIncomeSettlementEditDTO> addList = dtoList.stream().filter(dto -> !Optional.ofNullable(dto.getId()).isPresent()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(addList)){
            List<CostIncomeSettlement> addSettlementList = addList.stream().map(u -> {
                CostIncomeSettlement settlement = BeanUtil.copyProperties(u, CostIncomeSettlement.class);
                // 结算单编号生成规则：识别号+年月日+5位识别号，例：JSD2025021900001；
                String settlementNumber = RedisConstant.SETTLEMENT_NUMBER_PREFIX + LocalDate.now().toString() +
                        String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_NUMBER));
                settlement.setSettlementNumber(settlementNumber);
                settlement.setDataSources(NumberUtils.INTEGER_ONE);
                BaseBuildEntityUtil.buildInsert(settlement);
                return settlement;
            }).collect(Collectors.toList());
            costIncomeSettlementList.addAll(addSettlementList);
        }
        this.saveOrUpdateBatch(costIncomeSettlementList);
        return costIncomeSettlementList;
    }

    @Override
    public ApiResult<String> importExcel(Long projectId, List<CostIncomeSettlementImportDTO> importDTOList) {
        List<CostIncomeSettlement> costIncomeSettlementList = new ArrayList<>();
        if (CollUtil.isEmpty(importDTOList)) {
            return ApiResult.success("无数据导入");
        }
        StringBuilder error = new StringBuilder();
        AtomicInteger i = new AtomicInteger(2);
        // 校验
        importDTOList.forEach(importDTO ->{
            this.checkImport(importDTO, error, i.getAndIncrement());
        });
        if (error.toString().contains("不")) {
            return ApiResult.failure("导入失败,原因是" + error.toString());
        }
        // 获取税率字典
        Map<String,String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getName,
                        DictKvVo::getValue));
        List<String> detailsNumbers = importDTOList.stream().map(CostIncomeSettlementImportDTO::getSettlementNumber).collect(Collectors.toList());
        Map<String, CostIncomeSettlement> detailMapByDetailsNumber = baseMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery()
                .in(CostIncomeSettlement::getSettlementNumber, detailsNumbers))
                .stream().collect(Collectors.toMap(CostIncomeSettlement::getSettlementNumber, a -> a));
        costIncomeSettlementList = importDTOList.stream().map(u -> {
            CostIncomeSettlement settlement = BeanUtil.copyProperties(u, CostIncomeSettlement.class);
            CostIncomeSettlement existsDetail = detailMapByDetailsNumber.get(u.getSettlementNumber());
            if (Optional.ofNullable(existsDetail).isPresent()){
                settlement.setId(existsDetail.getId());
            }else {
                settlement.setId(IdWorker.getId());
                settlement.setProjectId(projectId);
                // 结算单编号生成规则：识别号+年月日+5位识别号，例：JSD2025021900001；
                String settlementNumber = RedisConstant.SETTLEMENT_NUMBER_PREFIX + LocalDate.now().toString() +
                        String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_NUMBER));
                settlement.setSettlementNumber(settlementNumber);
                settlement.setDataSources(NumberUtils.INTEGER_ONE);
            }
            //结算含税金额
            BigDecimal budgetAmountIncludedTax = new BigDecimal(u.getBudgetAmountIncludedTax());
            settlement.setBudgetAmountIncludedTax(budgetAmountIncludedTax);
            //税率
            settlement.setTaxRate(taxRateMap.getOrDefault(u.getTaxRate(),null));
            BigDecimal taxRate = new BigDecimal(u.getTaxRate().replaceAll("%", "")).divide(new BigDecimal(100));
            //结算不含税金额
            BigDecimal budgetAmountExcludingTax = budgetAmountIncludedTax.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP);
            settlement.setBudgetAmountExcludingTax(budgetAmountExcludingTax);

            BaseBuildEntityUtil.buildUpdate(settlement);
            return settlement;
        }).collect(Collectors.toList());
        this.saveOrUpdateBatch(costIncomeSettlementList);
        return ApiResult.success("导入成功");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult<String> delete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ApiResult.success("无数据删除");
        }
        //删除结算明细
        List<CostIncomeSettlement> settlementList = baseMapper
                .selectList(Wrappers.<CostIncomeSettlement>lambdaQuery().in(CostIncomeSettlement::getId, ids));
        baseMapper.delByIds(ids);
        List<CostIncomeSettlementDetail> detailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                .in(CostIncomeSettlementDetail::getSettlementNumber, settlementList.stream().map(CostIncomeSettlement::getSettlementNumber).collect(Collectors.toList()))
                .in(CostIncomeSettlementDetail::getTaxRate, settlementList.stream().map(CostIncomeSettlement::getTaxRate).collect(Collectors.toList())));
        if (CollectionUtil.isNotEmpty(detailList)){
            List<Long> detailIds = detailList.stream().map(CostIncomeSettlementDetail::getId).collect(Collectors.toList());
            List<Long> calculationDetailIds = detailList.stream().map(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId).collect(Collectors.toList());
            //删除结算明细
            settlementDetailMapper.delByIds(detailIds);
            List<CostIncomeSettlementDetail> noDeleteDetailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                    .in(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId, calculationDetailIds));
            Map<Long, List<CostIncomeSettlementDetail>> noDeleteDetailMap = noDeleteDetailList.stream().collect(Collectors.groupingBy(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId));
            // 修改收入测算-结算状态
            if (CollectionUtil.isNotEmpty(calculationDetailIds)){
                // 修改收入测算明细-结算状态
                List<CostIncomeCalculationDetail> calculationDetailList = CollUtil.emptyIfNull(calculationDetailMapper.selectBatchIds(calculationDetailIds)).stream()
                        .collect(Collectors.toList());
                List<CostIncomeCalculationDetail> updateCalculationDetailList = calculationDetailList.stream()
                        .filter(c -> noDeleteDetailMap.getOrDefault(c.getId(),ListUtil.empty()).size() == 0).collect(Collectors.toList());
                updateCalculationDetailList.forEach(item -> {
                    item.setSettlementStatus(SettlementStatusEnum.AWAIT_SETTLEMENT.getValue());
                    BaseBuildEntityUtil.buildUpdate(item);
                });
                if (CollectionUtil.isNotEmpty(updateCalculationDetailList)){
                    calculationDetailMapper.batchUpdate(updateCalculationDetailList);
                    // 关联对应的汇总数据状态
                    PigxUser user = SecurityUtils.getUser();
                    Map<Long, Integer> calculationSettlementMap =
                            getCalculationStatusMap(updateCalculationDetailList.stream().map(CostIncomeCalculationDetail::getCalculationId).collect(Collectors.toList()));
                    calculationMapper.batchUpdateStatusByDetails(calculationSettlementMap, NumberUtils.INTEGER_ONE, user.getId(), user.getName());
                }
            }
        }

        return ApiResult.success("删除成功");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> createSettlementRequest(CostIncomeSettlementRequestDTO dto) {
        //获得当前用户免登录token
        String redirectOaToken;
        Long currentUserId;
        try {
            PigxUser user = SecurityUtils.getUser();
            currentUserId = user.getId();
            redirectOaToken = remoteOaService.getOaSsoTokenByUserId(currentUserId).getData();
            log.info("获得当前用户免登录token:{}", redirectOaToken);
        } catch (Exception e) {
            log.error("获取跳转token失败:{}", e.getMessage());
            return R.failed("获取跳转token失败" + e.getMessage());
        }
        if (StrUtil.isBlank(redirectOaToken)) {
            log.error("获取跳转token失败:token为空");
            return R.failed("获取跳转token失败:token为空");
        }

        OaAccountVO oaAccountVO = dbApiUtil.getOaAccountInfoByUserId(currentUserId);
        log.info("OA用户:{}", oaAccountVO);
        if (!Optional.ofNullable(oaAccountVO).isPresent()) {
            return R.failed("发起人力外包结算审批流程失败，原因:未查询到当前用户");
        } else {
            if (!Optional.ofNullable(oaAccountVO.getOaId()).isPresent()) {
                return R.failed("发起人力外包结算审批流程失败，原因:oaId为空");
            }
        }
        //项目信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(dto.getProjectId());
        Long requestId;
        if (projectInfo == null) {
            log.error("根据id:{},没有找到对应项目数据", dto.getProjectId());
            return R.failed("根据id:" + dto.getProjectId() + ",没有找到对应项目数据");
        }
        try {
            requestId = generateRequestId(dto, oaAccountVO, projectInfo);
        } catch (Exception e) {
            log.error("发起OA流程失败:" + e);
            e.printStackTrace();
            return R.failed("发起OA流程失败:" + e.getMessage());
        }
        return R.ok(StrUtil.format(workflowUrl, redirectOaToken, requestId), "请求成功");
    }

    @Override
    public ApiResult<Boolean> isCreateSettlementRequest(String projectId) {
        List<ProjectBusinessMilestones> businessMilestonesList = projectBusinessMilestonesMapper.selectList(Wrappers.<ProjectBusinessMilestones>lambdaQuery().eq(ProjectBusinessMilestones::getProjectId, projectId)).stream().collect(Collectors.toList());
        businessMilestonesList = businessMilestonesList.stream().filter(item -> item.getType() == 2 && "1".equals(item.getIfFinish())).collect(Collectors.toList());
        Boolean flag = Boolean.FALSE;
        if (CollectionUtil.isNotEmpty(businessMilestonesList)) {
            flag= Boolean.TRUE;
        }
        return ApiResult.success(flag);
    }


    /**
     * 构建流程主表数据
     * @param dto
     * @param oaAccountVO
     * @param projectInfo
     * @return
     * @throws JsonProcessingException
     */
    private Long generateRequestId(CostIncomeSettlementRequestDTO dto, OaAccountVO oaAccountVO, ProjectInfo projectInfo)  throws JsonProcessingException {
        Long requestId;
        List<CostIncomeSettlement> settlementList = new ArrayList<>();
        List<CostIncomeSettlementDetail> detailList = new ArrayList<>();
        //获取收入结算信息
        List<CostIncomeSettlementRequestDetailDTO> requestDetailDTOList = new ArrayList<>();
        if (dto.getIsSummary()) {
            //查询收入结算汇总数据
            settlementList = baseMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery().in(
                    CostIncomeSettlement::getId, dto.getIds()));
            requestDetailDTOList = settlementList.stream().map(s -> {
                String requestIdStr = StrUtil.EMPTY;
                if (Optional.ofNullable(s.getRequestId()).isPresent()){
                    List<String> requestIds = Arrays.asList(s.getRequestId().split(","));
                    requestIdStr = requestIds.size()==1 ? requestIds.get(0) :StrUtil.EMPTY;
                }
                return CostIncomeSettlementRequestDetailDTO.builder()
                        .projectId(s.getProjectId())
                        .startDate(s.getStartDate())
                        .endDate(s.getEndDate())
                        .budgetAmountExcludingTax(s.getBudgetAmountExcludingTax())
                        .taxRate(s.getTaxRate())
                        .budgetAmountIncludedTax(s.getBudgetAmountIncludedTax())
                        .number(s.getSettlementNumber())
                        .requestId(StrUtil.isNotBlank(requestIdStr)?Long.valueOf(requestIdStr):null)
                        .build();
            }).collect(Collectors.toList());
            //根据结算单号和税率查询收入结算明细
            detailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                    .in(CostIncomeSettlementDetail::getSettlementNumber, settlementList.stream().map(CostIncomeSettlement::getSettlementNumber).collect(Collectors.toList()))
                    .in(CostIncomeSettlementDetail::getTaxRate, settlementList.stream().map(CostIncomeSettlement::getTaxRate).collect(Collectors.toList()))

            );
        } else {
            //查询收入结算明细数据
            detailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery().in(
                    CostIncomeSettlementDetail::getId, dto.getIds()));
            requestDetailDTOList = detailList.stream().map(s -> {
                return CostIncomeSettlementRequestDetailDTO.builder()
                        .projectId(s.getProjectId())
                        .startDate(s.getStartDate())
                        .endDate(s.getEndDate())
                        .budgetAmountExcludingTax(s.getBudgetAmountExcludingTax())
                        .taxRate(s.getTaxRate())
                        .budgetAmountIncludedTax(s.getBudgetAmountIncludedTax())
                        .number(s.getSettlementDetailsNumber())
                        .requestId(s.getRequestId())
                        .build();
            }).collect(Collectors.toList());
            //根据结算单号和税率查询收入结算汇总
            settlementList = baseMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery()
                    .in(CostIncomeSettlement::getSettlementNumber, detailList.stream().map(CostIncomeSettlementDetail::getSettlementNumber).collect(Collectors.toList()))
                    .in(CostIncomeSettlement::getTaxRate, detailList.stream().map(CostIncomeSettlementDetail::getTaxRate).collect(Collectors.toList()))

            );
        }
        //筛选已发起过流程的收入结算
        List<CostIncomeSettlementRequestDetailDTO> requestList = requestDetailDTOList.stream().filter(d -> Optional.ofNullable(d.getRequestId()).isPresent()).collect(Collectors.toList());
        Set<Long> requestIdsList = requestList.stream().map(CostIncomeSettlementRequestDetailDTO::getRequestId).collect(Collectors.toSet());
        //发起人力结算审批流程
        OaCreateRequestDTO oaCreateRequestDTO = new OaCreateRequestDTO();
        //请求参数
        List<OaMainParamDTO> mainDataList = new ArrayList<>();

        //流程id
        oaCreateRequestDTO.setWorkflowId(dbApiUtil.getWorkflowIdByName(OAFormTypeEnum.RLWBJSSP.getName()));
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("ApplicantID")
                .fieldValue(String.valueOf(oaAccountVO.getOaId()))
                .build());
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("bh")
                .fieldValue(String.valueOf(oaAccountVO.getWorkcode()))
                .build());
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("ApplicantDeptID")
                .fieldValue(oaAccountVO.getDepartmentid())
                .build());
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("ApplicantTel")
                .fieldValue(oaAccountVO.getMobile())
                .build());
        //岗位
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("ApplicantJobID")
                .fieldValue(oaAccountVO.getJobtitle())
                .build());
        //合同所属项目名称
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("htssxmmc")
                .fieldValue(String.valueOf(projectInfo.getId()))
                .build());

        // 是否已有商务里程碑（写死：0否）
        mainDataList.add(OaMainParamDTO.builder()
                .fieldName("not_milestone")
                .fieldValue("0")
                .build());
        //添加明细表数据
        //明细表数据
        List<Map<String, Object>> detailDataList = new ArrayList<>();
        Map<String, Object> detailDataMap = new LinkedHashMap<>();
        addCostFlowRequest(requestDetailDTOList, detailDataMap);
        String oaTitle = getOaFlowReqTitle(OAFormTypeEnum.RLWBJSSP.getName());
        oaCreateRequestDTO.setRequestName(oaTitle);
        oaCreateRequestDTO.setMainData(mainDataList);
        String mainParamStr = new ObjectMapper().writeValueAsString(mainDataList);
        // 获取token
        String token = oaUtil.getToken();
        if (oaAccountVO.getOaId() == null) {
            throw new ServiceException("必须传入userID");
        }
        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);
        //!=1重新发起流程
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> otherParamsMap = new HashMap<>();
        List<CostManageVersionVO> oaRequest = dbApiUtil.getOARequest(requestIdsList);
        if(1 == requestList.size() && CollectionUtil.isEmpty(oaRequest)){
            CostIncomeSettlementRequestDetailDTO requestDetail = requestList.get(NumberUtils.INTEGER_ZERO);
            //查询人力外包结算单审批表明细
            List<SettlementDetailVO> mainList = dbApiUtil.getSettlementDetail(requestDetail.getRequestId());
            String deleteKeys = mainList.stream().map(SettlementDetailVO::getId).collect(Collectors.toList())
                    .stream().map(Objects::toString).collect(Collectors.joining(","));
            detailDataMap.put("deleteAll", "1");
            detailDataMap.put("deleteKeys", deleteKeys);
            detailDataList.add(detailDataMap);
            String detailParamStr = new ObjectMapper().writeValueAsString(detailDataList);

            //动作类型:保存
            otherParamsMap.put("src", "save");
            oaCreateRequestDTO.setOtherParams(otherParamsMap);
            String otherParamsStr = JSONUtil.toJsonStr(otherParamsMap);
            map = oaClient.submitRequest(token, appId, encryptUserId, url,
                    requestDetail.getRequestId().toString(),
                    detailParamStr,
                    mainParamStr,
                    otherParamsStr
                    );
            JSONObject jsonObject = JSONUtil.parseObj(map);
            String code = jsonObject.getStr("code");
            if(!"SUCCESS".equals(code)){
                log.error("发起OA流程失败1，原因:{}", map);
                throw new BusinessException(map.toString());
            }
            requestId = requestDetail.getRequestId();
        }else {
            detailDataList.add(detailDataMap);
            String detailParamStr = new ObjectMapper().writeValueAsString(detailDataList);
            //是否主动提交流程
            otherParamsMap.put("isnextflow", "0");
            oaCreateRequestDTO.setOtherParams(otherParamsMap);
            String otherParamsStr = JSONUtil.toJsonStr(otherParamsMap);

            map = oaClient.doCreateRequest(token, appId, encryptUserId, url, oaCreateRequestDTO.getWorkflowId().toString(),
                    oaCreateRequestDTO.getRequestName(),
                    mainParamStr,
                    otherParamsStr,
                    detailParamStr);
            JSONObject jsonObject = JSONUtil.parseObj(map);
            String code = jsonObject.getStr("code");
            if (!"SUCCESS".equals(code)) {
                log.error("发起OA流程失败2，原因:{}", map);
                throw new ServiceException("发起OA流程失败2，原因:" + map);
            }
            JSONObject dataMap = jsonObject.getJSONObject("data");
            //流程id
            requestId = dataMap.getLong("requestid");
        }

        //赋值OA结算单流程id
        if (CollectionUtil.isNotEmpty(detailList)){
            List<CostIncomeSettlementDetail> updateDetails = new ArrayList<>();
            detailList.stream().forEach(d ->{
                d.setRequestId(requestId);
                BaseBuildEntityUtil.buildUpdate(d);
            });
            updateDetails.addAll(detailList);
            List<CostIncomeSettlementDetail> updateDetailList = settlementDetailMapper
                    .selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                            .eq(CostIncomeSettlementDetail::getRequestId, requestId))
                    .stream().filter(d-> !dto.getIds().contains(d.getId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(updateDetailList)){
                updateDetailList.stream().forEach(d ->{
                    d.setRequestId(null);
                    BaseBuildEntityUtil.buildUpdate(d);
                });
                updateDetails.addAll(updateDetailList);
            }
            settlementDetailMapper.batchUpdate(updateDetails);
        }

        if (dto.getIsSummary()){
            settlementList.stream().forEach(s ->{
                s.setRequestId(requestId.toString());
                BaseBuildEntityUtil.buildUpdate(s);
            });
        }else {
            List<CostIncomeSettlementDetail> settlementDetailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                    .in(CostIncomeSettlementDetail::getSettlementNumber, settlementList.stream().map(CostIncomeSettlement::getSettlementNumber).collect(Collectors.toList()))
                    .in(CostIncomeSettlementDetail::getTaxRate, settlementList.stream().map(CostIncomeSettlement::getTaxRate).collect(Collectors.toList())));
            Table<String, String, List<CostIncomeSettlementDetail>> detailTable = HashBasedTable.create();
            if (CollectionUtil.isNotEmpty(settlementDetailList)){
                settlementDetailList.stream().forEach(d ->{
                    List<CostIncomeSettlementDetail> exists = detailTable.get(d.getSettlementNumber(),d.getTaxRate());
                    if (CollectionUtil.isEmpty(exists)) {
                        detailTable.put(d.getSettlementNumber(),d.getTaxRate(), Lists.newArrayList(d));
                    } else {
                        exists.add(d);
                    }
                });
            }
            settlementList.stream().forEach(s ->{
                List<CostIncomeSettlementDetail> detailListByNumberTaxRate = detailTable.get(s.getSettlementNumber(), s.getTaxRate());
                Set<Long> requestIdList = detailListByNumberTaxRate.stream().filter(d-> Optional.ofNullable(d.getRequestId()).isPresent()).map(CostIncomeSettlementDetail::getRequestId).collect(Collectors.toSet());
                String result = requestIdList.stream().map(Objects::toString).collect(Collectors.joining(","));
                s.setRequestId(result);
                BaseBuildEntityUtil.buildUpdate(s);
            });
        }
        baseMapper.batchUpdate(settlementList);

        return requestId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementNumber(List<CostIncomeSettlement> settlementList, List<CostIncomeSettlementDetail> detailList, Long requestId) {
        if (CollUtil.isNotEmpty(settlementList)) {
            //查询收入结算汇总数据
            //根据结算单号和税率查询收入结算明细
            detailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                    .in(CostIncomeSettlementDetail::getSettlementNumber, settlementList.stream().map(CostIncomeSettlement::getSettlementNumber).collect(Collectors.toList()))
                    .in(CostIncomeSettlementDetail::getTaxRate, settlementList.stream().map(CostIncomeSettlement::getTaxRate).collect(Collectors.toList()))

            );
        } else {
            //查询收入结算明细数据
            //根据结算单号和税率查询收入结算汇总
            settlementList = baseMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery()
                    .in(CostIncomeSettlement::getSettlementNumber, detailList.stream().map(CostIncomeSettlementDetail::getSettlementNumber).collect(Collectors.toList()))
                    .in(CostIncomeSettlement::getTaxRate, detailList.stream().map(CostIncomeSettlementDetail::getTaxRate).collect(Collectors.toList()))

            );
        }

        if (CollectionUtil.isNotEmpty(detailList)) {
            detailList.forEach(d -> {
                d.setRequestId(requestId);
                BaseBuildEntityUtil.buildUpdate(d);
            });
            settlementDetailMapper.batchUpdate(detailList);
        }
        // 汇总
        if (CollectionUtil.isNotEmpty(settlementList)) {
            settlementList.forEach(s -> {
                s.setRequestId(requestId.toString());
                BaseBuildEntityUtil.buildUpdate(s);
            });
        } else {
            List<String> settlementNumberList = CollStreamUtil.toList(settlementList, CostIncomeSettlement::getSettlementNumber);
            List<String> taxRateList = settlementList.stream().map(CostIncomeSettlement::getTaxRate).collect(Collectors.toList());
            List<CostIncomeSettlementDetail> settlementDetailList = settlementDetailMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                    .in(CostIncomeSettlementDetail::getSettlementNumber, settlementNumberList)
                    .in(CostIncomeSettlementDetail::getTaxRate, taxRateList));
            Table<String, String, List<CostIncomeSettlementDetail>> detailTable = HashBasedTable.create();
            if (CollectionUtil.isNotEmpty(settlementDetailList)) {
                settlementDetailList.forEach(d -> {
                    List<CostIncomeSettlementDetail> exists = detailTable.get(d.getSettlementNumber(), d.getTaxRate());
                    if (CollectionUtil.isEmpty(exists)) {
                        detailTable.put(d.getSettlementNumber(), d.getTaxRate(), Lists.newArrayList(d));
                    } else {
                        exists.add(d);
                    }
                });
            }
            settlementList.forEach(s -> {
                List<CostIncomeSettlementDetail> detailListByNumberTaxRate = detailTable.get(s.getSettlementNumber(), s.getTaxRate());
                Set<Long> requestIdList = CollUtil.emptyIfNull(detailListByNumberTaxRate).stream().map(CostIncomeSettlementDetail::getRequestId).collect(Collectors.toSet());
                String result = requestIdList.stream().map(Objects::toString).collect(Collectors.joining(","));
                s.setRequestId(result);
                BaseBuildEntityUtil.buildUpdate(s);
            });
        }
        baseMapper.batchUpdate(settlementList);
    }

    /**
     * 拼接流程标题
     * @param formTypeName
     * @return
     */
    private static String getOaFlowReqTitle(String formTypeName) {
        return StrUtil.format(formTypeName + "-{}-{}"
                , SecurityUtils.getUser().getName()
                , LocalDate.now()
        );
    }

    /**
     * 构建明细表数据
     * @param detailDTOList
     * @param detailDataMap
     */
    private void addCostFlowRequest(List<CostIncomeSettlementRequestDetailDTO> detailDTOList, Map<String, Object> detailDataMap) {

        if (CollectionUtil.isNotEmpty(detailDTOList)) {
            //构建明细表数据
            detailDataMap.put("tableDBName", OAFormTypeEnum.RLWBJSSP.getDetailTable());
            List<Map<String, Object>> tableRecordList = new ArrayList<>();
            detailDTOList.forEach(
                    detail -> {
                        List<OaMainParamDTO> tableFieldList = new ArrayList<>();

                        //订单开始日期
                        if (detail.getStartDate() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("ddksrq")
                                    .fieldValue(String.valueOf(detail.getStartDate())).build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //订单结算日期
                        if (detail.getEndDate() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("ddjsrq")
                                    .fieldValue(String.valueOf(detail.getEndDate())).build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //结算金额(含税)
                        if (detail.getBudgetAmountIncludedTax() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("yjsfkjehs")
                                    .fieldValue(String.valueOf(detail.getBudgetAmountIncludedTax())).build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //税率
                        if (detail.getTaxRate() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("sl")
                                    .fieldValue(String.valueOf(detail.getTaxRate())).build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //结算金额(不含税)
                        if (detail.getBudgetAmountExcludingTax() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("yjsfkjebhs")
                                    .fieldValue(String.valueOf(detail.getBudgetAmountExcludingTax())).build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //款项名称-人力结算款
                        if (detail.getBudgetAmountExcludingTax() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("kxmci")
                                    .fieldValue("6").build();
                            tableFieldList.add(oaMainParamDTO);
                        }
                        //ID标签
                        if (detail.getNumber() != null) {
                            OaMainParamDTO oaMainParamDTO = OaMainParamDTO.builder().fieldName("idtag")
                                    .fieldValue(detail.getNumber()).build();
                            tableFieldList.add(oaMainParamDTO);
                            //URL隐藏
                            String settlementUrl = redirectPrefix +
                                    Base64.encode(CharSequenceUtil.format(costIncomeSettlementUrlRedirect, detail.getProjectId(),detail.getNumber()), Charsets.UTF_8);
                            OaMainParamDTO oaMainParamDTOUrl = OaMainParamDTO.builder().fieldName("url")
                                    .fieldValue(settlementUrl).build();
                            tableFieldList.add(oaMainParamDTOUrl);
                        }
                        // 商务里程碑（写死：完成结算单签署）
                        OaMainParamDTO oaMainParamDTO1 = OaMainParamDTO.builder().fieldName("swlcb")
                                .fieldValue("完成结算单签署").build();
                        tableFieldList.add(oaMainParamDTO1);
                        // 是否已完成（写死：0是）
                        OaMainParamDTO oaMainParamDTO2 = OaMainParamDTO.builder().fieldName("sfwc")
                                .fieldValue("0").build();
                        tableFieldList.add(oaMainParamDTO2);

                        Map<String, Object> tableRecordMap = new HashMap<>();
                        tableRecordMap.put("recordOrder", 0);
                        tableRecordMap.put("workflowRequestTableFields", tableFieldList);
                        tableRecordList.add(tableRecordMap);
                    }
            );
            detailDataMap.put("workflowRequestTableRecords", tableRecordList);

        }
    }

    /**
     * 校验导入参数
     *
     * @param dto {@link CostIncomeSettlementImportDTO}
     * @param errorAll {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(CostIncomeSettlementImportDTO dto,StringBuilder errorAll, Integer row) {
        StringBuilder error = new StringBuilder();
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getStartDate())) {
            error.append("结算开始日期不能为空!");
        }else {
            String regex = "^\\d{4}/(\\d{2}|\\d)/(\\d{2}|\\d)$";
            if (!dto.getStartDate().matches(regex)){
                error.append("结算开始日期不符合日期格式!");
            }
        }
        if (CharSequenceUtil.isBlank(dto.getEndDate())) {
            error.append("结算截止日期不能为空!");
        }else{
            String regex = "^\\d{4}/(\\d{2}|\\d)/(\\d{2}|\\d)$";
            if (!dto.getEndDate().matches(regex)){
                error.append("结算截止日期不符合日期格式!");
            }
        }
        if (CharSequenceUtil.isBlank(dto.getBudgetAmountIncludedTax())){
            error.append("结算含税金额不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getTaxRate())) {
            error.append("税率不能为空!");
        }
        if (CharSequenceUtil.isNotBlank(dto.getSettlementNumber())) {
            String regex = "JSD\\d{4}\\d{2}\\d{2}\\d{5}";
            if (!dto.getSettlementNumber().matches(regex)){
                error.append("结算单编号不符合生成规则!");
            }
        }
        if (error.toString().contains("不")){
            error.append("<br/>");
            errorAll.append(error);
        }
    }

    /**
     * 获取明细对应汇总数据状态集合
     *
     * @param calculationIds 汇总ID集合
     * @return
     */
    public Map<Long, Integer> getCalculationStatusMap(List<Long> calculationIds) {
        List<CostIncomeCalculationDetail> detailList =
                Optional.ofNullable(calculationDetailMapper.findList(CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build()))
                        .orElse(ListUtil.empty());
        Map<Long, Integer> calculationSettlementMap = detailList.stream().collect(Collectors.groupingBy(CostIncomeCalculationDetail::getCalculationId,
                Collectors.mapping(CostIncomeCalculationDetail::getSettlementStatus,
                        Collectors.collectingAndThen(Collectors.toList(), statuses -> {
                                    Set<Integer> uniqueValues = new HashSet<>(statuses);
                                    return uniqueValues.size() == 1 ? uniqueValues.iterator().next() : SettlementStatusEnum.PART_SETTLEMENT.getValue();
                                }
                        ))));
        return calculationSettlementMap;
    }
}
