package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.PaymentDataDTO;
import com.gok.pboot.pms.entity.vo.ProjectDataVO;
import com.gok.pboot.pms.service.IProjectDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 项目核心数据控制器
 *
 * <AUTHOR>
 * @menu 项目核心数据
 * @date 2023-07-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/projectData")
public class ProjectDataController {

    private final IProjectDataService service;

    /**
     * 根据项目ID获取核心数据
     *
     * @param id 项目ID
     * @return data
     */
    @GetMapping("/get/{id}")
    public ApiResult<ProjectDataVO> getById(@PathVariable("id") Long id) {
        return ApiResult.success(service.findById(id));
    }

    /**
     * 根据项目ID获取质保确认/关闭确认数据
     *
     * @param id 项目ID
     * @return data
     */
    @GetMapping("/getConfirmedData/{id}")
    public ApiResult<ProjectDataVO> getConfirmedDataById(@PathVariable("id") Long id) {
        return ApiResult.success(service.findConfirmedDataByProjectId(id));
    }

    /**
     * 更新项目偏差说明
     *
     * @param request 更新请求
     * @return 项目ID
     */
    @PutMapping("/deviation")
    public ApiResult<Long> updateDeviation(@Valid @RequestBody PaymentDataDTO request) {
        return ApiResult.success(service.updateDeviation(request));
    }

}