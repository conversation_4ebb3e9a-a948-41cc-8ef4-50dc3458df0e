package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverPurchasePlan;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverPurchasePlanAcceptance;
import com.gok.pboot.pms.cost.entity.dto.CostDeliverPurchasePlanDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.dto.DeliverCostBudgetListDto;
import com.gok.pboot.pms.cost.entity.dto.DeliverExpensesReimburseListDto;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.AccountTypeEnum;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostDeliverExpensesReimburseMapper;
import com.gok.pboot.pms.cost.mapper.CostDeliverPurchasePlanAcceptanceMapper;
import com.gok.pboot.pms.cost.mapper.CostDeliverPurchasePlanMapper;
import com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper;
import com.gok.pboot.pms.cost.service.ICostDeliverExpensesReimburseService;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import com.gok.pboot.pms.entity.domain.ProjectData;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.ProjectDataMapper;
import com.gok.pboot.pms.service.IDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 成本交付管理费用报销 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostDeliverExpensesReimburseServiceImpl extends ServiceImpl<CostDeliverExpensesReimburseMapper, CostDeliverExpensesReimburse> implements ICostDeliverExpensesReimburseService {
    private final CostManageEstimationResultsMapper costManageEstimationResultsMapper;
    private final ICostManageVersionService costManageVersionService;
    private final IDictService idictService;
    private final CostDeliverPurchasePlanMapper purchasePlanMapper;
    private final CostDeliverPurchasePlanAcceptanceMapper purchasePlanAcceptanceMapper;
    private final ProjectDataMapper projectDataMapper;


    @Override
    public List<DeliverCostBudgetListVO> findDeliverCostBudget(DeliverCostBudgetListDto dto) {
        //查询最新且确认版本
        List<CostManageVersionVO> costManageVersionVOList = costManageVersionService.getHistoryVersions(
                new PageRequest(1, 1000),
                CostManageVersionDTO.builder().projectId(dto.getProjectId()).build()
        ).getRecords();
        CostManageVersionVO costManageVersionVO = costManageVersionVOList.stream().filter(
                costManageVersion -> CostManageStatusEnum.CONFIRMED.getValue().equals(costManageVersion.getStatus())).findFirst().orElse(null);
        if (ObjectUtil.isEmpty(costManageVersionVO)) {
            return new ArrayList<>();
        }
        List<DeliverCostBudgetListVO> costBudgetListList =
                costManageEstimationResultsMapper.findCostBudgetByVersionId(costManageVersionVO.getVersionId(), dto.getCostType());
        switch (CostTypeEnum.fromValue(dto.getCostType())) {
            case FYBX: // 查询费用报销预算明细
                getUsedCostByReimburse(dto.getProjectId(), costBudgetListList);
                break;
            case WCFY: // 查询外采费用预算明细
                getUsedCostByPurchase(dto.getProjectId(), costBudgetListList);
                break;
            default:
                return new ArrayList<>();
        }
        return costBudgetListList;
    }


    /**
     * 通过费用报销查询成本预算明细对应已用预算、剩余预算
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    @Override
    public void getUsedCostByReimburse(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList) {
        List<CostDeliverExpensesReimburse> expensesReimburseList = baseMapper.selReimburseList(projectId);
        calculateCostBudget(costBudgetListList, expensesReimburseList);
    }

    /**
     * 通过采购付款查询成本预算明细对应已用预算、剩余预算
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    @Override
    public void getUsedCostByPurchase(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList) {
        List<CostDeliverExpensesReimburse> expensesPurchaseList = baseMapper.selPurchaseList(projectId);
        calculateCostBudget(costBudgetListList, expensesPurchaseList);
    }

    /**
     * 计算预算明细对应已用预算、剩余预算
     *
     * @param costBudgetListList
     * @param expensesReimburseList
     */
    private void calculateCostBudget(List<DeliverCostBudgetListVO> costBudgetListList, List<CostDeliverExpensesReimburse> expensesReimburseList) {
        Map<String, List<CostDeliverExpensesReimburse>> reimburseMap = expensesReimburseList.stream()
                .collect(Collectors.groupingBy(a ->
                        a.getAccountId() + String.valueOf(a.getTaxRate())
                ));

        // VO类组装
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        costBudgetListList.forEach(c -> {
            c.setAccountTypeTxt(EnumUtils.getNameByValue(AccountTypeEnum.class, c.getAccountType()));
            String taxRateTxt = null != c.getTaxRate() ? taxRateMap.get(c.getTaxRate()) : null;
            c.setTaxRateTxt(taxRateTxt + ("0".equals(taxRateTxt) ? StringPool.PERCENT : StringPool.EMPTY));
            c.setCostBudgetTypeTxt(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, c.getCostBudgetType()));
            List<CostDeliverExpensesReimburse> reimburseList = reimburseMap.getOrDefault(c.getAccountOaId() + String.valueOf(c.getTaxRate()), ListUtil.empty());
            if (CollectionUtil.isNotEmpty(reimburseList)) {
                BigDecimal reimburseMoney = reimburseList.stream()
                        .map(CostDeliverExpensesReimburse::getReimburseMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                c.setUsedBudget(reimburseMoney);
                c.setRemainBudget(c.getBudgetAmountIncludedTax().subtract(reimburseMoney));
            }
        });
    }


    @Override
    public Page<DeliverExpensesReimburseListVO> findExpensesReimburse(DeliverExpensesReimburseListDto reimburseListDto) {
        Page<DeliverExpensesReimburseListVO> reimburseListVOList = baseMapper.findExpensesReimburse(Page.of(reimburseListDto.getPageNumber(), reimburseListDto.getPageSize()), reimburseListDto);

        return reimburseListVOList;
    }

    @Override
    public List<CostDeliverPurchasePlanListVO> findPlanList(Long projectId) {
        List<CostDeliverPurchasePlan> planList = purchasePlanMapper.selectList(Wrappers.<CostDeliverPurchasePlan>lambdaQuery()
                .eq(CostDeliverPurchasePlan::getProjectId, projectId));
        if (CollectionUtil.isEmpty(planList)) {
            return ListUtil.empty();
        }
        //验收和付款条件
        List<Long> ids = planList.stream().map(CostDeliverPurchasePlan::getId).collect(Collectors.toList());
        List<CostDeliverPurchasePlanAcceptance> acceptanceList = purchasePlanAcceptanceMapper.selectList(Wrappers.<CostDeliverPurchasePlanAcceptance>lambdaQuery()
                .in(CostDeliverPurchasePlanAcceptance::getCostDeliverPurchasePlanId, ids));
        Map<Long, List<CostDeliverPurchasePlanAcceptance>> acceptanceListMap = acceptanceList.stream().collect(Collectors.groupingBy(CostDeliverPurchasePlanAcceptance::getCostDeliverPurchasePlanId));
        //采购金额
        List<CostDeliverExpensesReimburse> expensesReimburseList = baseMapper.selPurchaseList(projectId);
        Map<String, List<CostDeliverExpensesReimburse>> reimburseMap = expensesReimburseList.stream()
                .collect(Collectors.groupingBy(a ->
                        String.valueOf(a.getAccountId()) + String.valueOf(a.getTaxRate())));


        //已归档的采购金额
        List<CostDeliverExpensesReimburse> fileExpensesReimburseList = baseMapper.selFilePurchaseList(projectId);
        Map<String, List<CostDeliverExpensesReimburse>> fileReimburseMap = fileExpensesReimburseList.stream()
                .collect(Collectors.groupingBy(a ->
                        String.valueOf(a.getAccountId()) + String.valueOf(a.getTaxRate())));
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        List<CostDeliverPurchasePlanListVO> voList = planList.stream().map(p -> {
            CostDeliverPurchasePlanListVO vo = BeanUtil.copyProperties(p, CostDeliverPurchasePlanListVO.class);
            List<CostDeliverPurchasePlanAcceptance> planAcceptanceList = acceptanceListMap.getOrDefault(p.getId(), ListUtil.empty());
            if (CollectionUtil.isNotEmpty(planAcceptanceList)) {
                vo.setAcceptanceNum(planAcceptanceList.size());
            }
            vo.setTaxRateTxt(null != vo.getTaxRate() ? taxRateMap.get(vo.getTaxRate()) : null);
            vo.setRemainBudget(vo.getBudgetAmountIncludedTax());
            List<CostDeliverExpensesReimburse> reimburseList = reimburseMap.getOrDefault(String.valueOf(p.getAccountOaId()) + String.valueOf(p.getTaxRate()), ListUtil.empty());
            if (CollectionUtil.isNotEmpty(reimburseList)) {
                BigDecimal reimburseMoney = reimburseList.stream()
                        .map(e -> e.getReimburseMoney())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setUsedBudget(reimburseMoney);
                vo.setRemainBudget(vo.getBudgetAmountIncludedTax().subtract(reimburseMoney));
            }
            List<CostDeliverExpensesReimburse> fileReimburseList = fileReimburseMap.getOrDefault(String.valueOf(p.getAccountOaId()) + String.valueOf(p.getTaxRate()), ListUtil.empty());

            if (CollectionUtil.isNotEmpty(fileReimburseList)) {
                BigDecimal paidAmount = fileReimburseList.stream()
                        .map(e -> e.getReimburseMoney())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setPaidAmount(paidAmount);
            }

            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    @Override
    public CostDeliverPurchasePlanVO getPlan(Long id) {
        CostDeliverPurchasePlanVO costDeliverPurchasePlanVO = new CostDeliverPurchasePlanVO();
        CostDeliverPurchasePlan plan = purchasePlanMapper.selectById(id);
        if (Optional.ofNullable(plan).isPresent()) {
            costDeliverPurchasePlanVO = BeanUtil.copyProperties(plan, CostDeliverPurchasePlanVO.class);
            //验收和付款条件
            List<CostDeliverPurchasePlanAcceptance> acceptanceList = purchasePlanAcceptanceMapper.selectList(Wrappers.<CostDeliverPurchasePlanAcceptance>lambdaQuery()
                    .eq(CostDeliverPurchasePlanAcceptance::getCostDeliverPurchasePlanId, id));
            List<CostDeliverPurchasePlanAcceptanceVO> voList = acceptanceList.stream().map(a -> {
                CostDeliverPurchasePlanAcceptanceVO vo = BeanUtil.copyProperties(a, CostDeliverPurchasePlanAcceptanceVO.class);
                vo.setAcceptStatusTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, vo.getAcceptStatus()));
                return vo;
            }).collect(Collectors.toList());
            costDeliverPurchasePlanVO.setVoList(voList);
        }
        return costDeliverPurchasePlanVO;
    }

    @Override
    public ApiResult<String> addOrEditPlan(CostDeliverPurchasePlanDTO dto) {
        // 1、获取数据
        CostDeliverPurchasePlan plan = purchasePlanMapper.selectById(dto.getId());
        List<CostDeliverPurchasePlanAcceptance> acceptanceList = new ArrayList<>();
        if (Optional.ofNullable(plan).isPresent()) {
            // 2.2、更新数据
            CostDeliverPurchasePlan costDeliverPurchasePlan = BeanUtil.copyProperties(dto, CostDeliverPurchasePlan.class);
            BaseBuildEntityUtil.buildUpdate(costDeliverPurchasePlan);

            purchasePlanMapper.updateById(costDeliverPurchasePlan);
            purchasePlanAcceptanceMapper.delete(Wrappers.<CostDeliverPurchasePlanAcceptance>lambdaQuery()
                    .eq(CostDeliverPurchasePlanAcceptance::getCostDeliverPurchasePlanId, dto.getId()));

            acceptanceList = dto.getVoList().stream().map(a -> {
                CostDeliverPurchasePlanAcceptance acceptance = BeanUtil.copyProperties(a, CostDeliverPurchasePlanAcceptance.class, "id");
                acceptance.setCostDeliverPurchasePlanId(dto.getId());
                BaseBuildEntityUtil.buildInsert(acceptance);
                return acceptance;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(acceptanceList)) {
                purchasePlanAcceptanceMapper.batchSave(acceptanceList);

            }
            return ApiResult.success("编辑成功");
        }
        // 3、新增操作
        CostDeliverPurchasePlan costDeliverPurchasePlan = BeanUtil.copyProperties(dto, CostDeliverPurchasePlan.class, "id");
        BaseBuildEntityUtil.buildInsert(costDeliverPurchasePlan);

        acceptanceList = dto.getVoList().stream().map(a -> {
            CostDeliverPurchasePlanAcceptance acceptance = BeanUtil.copyProperties(a, CostDeliverPurchasePlanAcceptance.class, "id");
            acceptance.setCostDeliverPurchasePlanId(costDeliverPurchasePlan.getId());
            BaseBuildEntityUtil.buildInsert(acceptance);
            return acceptance;
        }).collect(Collectors.toList());
        purchasePlanMapper.insert(costDeliverPurchasePlan);
        if (CollectionUtil.isNotEmpty(acceptanceList)) {
            purchasePlanAcceptanceMapper.batchSave(acceptanceList);
        }
        return ApiResult.success("新增成功");
    }

    @Override
    public ApiResult<String> delPlan(Long id) {
        // 1、获取数据
        CostDeliverPurchasePlan plan = purchasePlanMapper.selectById(id);
        if (!Optional.ofNullable(plan).isPresent()) {
            return ApiResult.failure("查询不到数据");
        }
        List<CostDeliverPurchasePlanAcceptance> acceptanceList = purchasePlanAcceptanceMapper.selectList(Wrappers.<CostDeliverPurchasePlanAcceptance>lambdaQuery()
                .eq(CostDeliverPurchasePlanAcceptance::getCostDeliverPurchasePlanId, id));
        List<Long> acceptanceIds = acceptanceList.stream().map(CostDeliverPurchasePlanAcceptance::getId).collect(Collectors.toList());

        // 3、删除数据
        purchasePlanMapper.delById(id);
        purchasePlanAcceptanceMapper.delByIds(acceptanceIds);
        return ApiResult.success("删除成功");
    }

    /**
     * 获取预估产值
     *
     * @param projectId 项目 ID
     * @return {@link BigDecimal }
     */
    @Override
    public BigDecimal getEstimatedTotalIncome(Long projectId) {
        // 1. 获取项目数据
        ProjectData projectData = projectDataMapper.selectById(projectId);
        if (projectData == null) {
            return BigDecimal.ZERO;
        }

        // 2. 获取外采预算不含税总额
        DeliverCostBudgetListDto dto = new DeliverCostBudgetListDto();
        dto.setProjectId(projectId);
        // 设置为外采费用类型
        dto.setCostType(CostTypeEnum.WCFY.getValue());
        List<DeliverCostBudgetListVO> costBudgetList = this.findDeliverCostBudget(dto);

        // 计算外采预算不含税总额
        BigDecimal outsourcingBudget = costBudgetList.stream()
                // 使用不含税金额
                .map(DeliverCostBudgetListVO::getBudgetAmountExcludingTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 确定使用哪个表的数据并计算
        BigDecimal totalBudgetRevenue;
        if (projectData.getTotalBudgetRevenue() != null
                && projectData.getTotalBudgetRevenue().compareTo(BigDecimal.ZERO) > 0) {
            // 使用B表数据
            totalBudgetRevenue = projectData.getTotalBudgetRevenue();
        } else {
            // 使用A表数据
            totalBudgetRevenue = projectData.getAbygsrzeBhs();
        }

        // 4. 计算预计总产值（预算收入总额-外采预算成本）
        if (totalBudgetRevenue == null) {
            return BigDecimal.ZERO;
        }

        return totalBudgetRevenue.subtract(outsourcingBudget);
    }

}
