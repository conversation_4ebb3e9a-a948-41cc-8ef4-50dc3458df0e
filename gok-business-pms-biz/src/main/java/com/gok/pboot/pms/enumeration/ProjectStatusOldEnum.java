//package com.gok.pboot.pms.enumeration;
//
//import lombok.Getter;
//
///**
// * 项目状态枚举
// *
// * <AUTHOR>
// * @since 2023-09-04
// */
//@Getter
//public enum ProjectStatusOldEnum implements ValueEnum<String> {
//
//    /**
//     * 商机
//     */
//    SJ("0", "商机"),
//
//    /**
//     * 商机终止
//     */
//    SJZZ("1", "商机中止"),
//
//    /**
//     * 在建
//     */
//    ZJ("2", "在建"),
//
//    /**
//     * 挂起
//     */
//    GQ("3", "挂起"),
//
////    /**
////     * 初验
////     */
////    CY("4", "初验"),
//
////    /**
////     * 终验
////     */
////    ZY("5", "终验"),
//
//    /**
//     * 结项
//     */
//    @Deprecated
//    JX("6", "结项"),
//
//    /**
//     * 异常终止
//     */
//    YCZZ("7", "异常中止"),
//
//    /**
//     * 质保
//     */
//    ZB("9", "质保"),
//
//    /**
//     * 关闭
//     */
//    CLOSE("10", "关闭"),
//
//    ;
//
//    private final String value;
//
//    private final String name;
//
//    ProjectStatusOldEnum(String value, String name) {
//        this.value = value;
//        this.name = name;
//    }
//
//}
