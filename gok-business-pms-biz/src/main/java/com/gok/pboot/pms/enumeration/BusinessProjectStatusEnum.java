package com.gok.pboot.pms.enumeration;

/**
 * 项目状态枚举
 **/
public enum BusinessProjectStatusEnum implements ValueEnum<Integer> {

    /**
     * 商机
     */
    SJ(0, "商机"),
    /**
     * 商机终止
     */
    SJZZ(1, "商机终止"),
    /**
     * 在建
     */
    ZJ(2, "在建"),
    /**
     * 挂起
     */
//    GQ(3, "挂起"),
    /**
     * 初验
     */
//    CY(4, "初验"),
    /**
     * 终验
     */
//    ZY(5, "终验"),
    /**
     * 结项
     */
    JX(6, "结项"),
    /**
     * 异常终止
     */
    YCZZ(7, "异常终止");

    //值
    private Integer  value;
    //名称
    private String name;

    BusinessProjectStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (BusinessProjectStatusEnum statusEnum : BusinessProjectStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (BusinessProjectStatusEnum statusEnum : BusinessProjectStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
