package com.gok.pboot.pms.entity.vo;


import lombok.Data;

/**
 * 项目关注
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
public class ProjectAttentionVO {
    /**
     * 关注ID
     */
    private Long attentionId;
    /**
     * 项目ID
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目状态（0=商机，1=商机终止，2=在建，3=挂起，4=初验，5=终验，6=结项，7=异常终止）
     */
    private Integer projectStatus;
    /**
     * 项目状态值（0=商机，1=商机终止，2=在建，3=挂起，4=初验，5=终验，6=结项，7=异常终止）
     */
    private String projectStatusName;

}
